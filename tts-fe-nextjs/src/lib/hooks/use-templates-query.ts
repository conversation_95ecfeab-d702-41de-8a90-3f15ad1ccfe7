'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';
import {
  getTemplates,
  getTemplate,
  createTemplate,
  updateTemplate,
  deleteTemplate,
  createStagedProductFromTemplate,
  createStagedProductFromTemplateAndUpload
} from '@/lib/api/services';
import { Template, CreateTemplateDto, UpdateTemplateDto } from '@/types/template';
import { PaginationQuery } from '@/types/pagination';
import { createQueryKeys } from '@/lib/api/api-client';
import { globalJobMonitor } from '@/lib/services/global-job-monitor';

// Create query keys for templates
export const templateKeys = createQueryKeys('templates');

/**
 * Hook for fetching templates with pagination
 */
export const useTemplates = (filters: PaginationQuery & { name?: string } = {}) => {
  const { data: session, status } = useSession();
  const isAuthenticated = status === 'authenticated';

  return useQuery({
    queryKey: templateKeys.list(filters),
    queryFn: () => getTemplates(filters, session?.backendToken),
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook for fetching a single template by ID
 */
export const useTemplate = (id: number) => {
  const { data: session, status } = useSession();
  const isAuthenticated = status === 'authenticated';

  return useQuery({
    queryKey: templateKeys.detail(id),
    queryFn: () => getTemplate(id, session?.backendToken),
    enabled: isAuthenticated && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook for creating a new template
 */
export const useCreateTemplate = () => {
  const { data: session } = useSession();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateTemplateDto) => createTemplate(data, session?.backendToken),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: templateKeys.lists() });
      toast.success('Template created successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to create template: ${error.message}`);
    },
  });
};

/**
 * Hook for updating an existing template
 */
export const useUpdateTemplate = (id: number) => {
  const { data: session } = useSession();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateTemplateDto) => updateTemplate(id, data, session?.backendToken),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: templateKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: templateKeys.lists() });
      toast.success('Template updated successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to update template: ${error.message}`);
    },
  });
};

/**
 * Hook for deleting a template
 */
export const useDeleteTemplate = () => {
  const { data: session } = useSession();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => deleteTemplate(id, session?.backendToken),

    // Optimistic update
    onMutate: async (deletedTemplateId) => {
      // Cancel any outgoing refetches to avoid overwriting our optimistic update
      await queryClient.cancelQueries({ queryKey: templateKeys.lists() });

      // Snapshot the previous value
      const previousTemplates = queryClient.getQueryData(templateKeys.lists());

      // Optimistically update the cache by removing the deleted template
      queryClient.setQueryData(
        templateKeys.lists(),
        (old: any) => {
          if (!old) return old;
          return {
            ...old,
            data: old.data.filter((template: any) => template.id !== deletedTemplateId)
          };
        }
      );

      // Return a context object with the snapshot
      return { previousTemplates };
    },

    // If the mutation fails, use the context returned from onMutate to roll back
    onError: (error: Error, _variables, context) => {
      if (context) {
        queryClient.setQueryData(templateKeys.lists(), context.previousTemplates);
      }
      toast.error(`Failed to delete template: ${error.message}`);
    },

    // Always refetch after error or success to ensure cache is in sync with server
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: templateKeys.lists() });
    },

    onSuccess: () => {
      toast.success('Template deleted successfully');
    }
  });
};

/**
 * Hook for creating a staged product from a template
 */
export const useCreateStagedProductFromTemplate = () => {
  const { data: session } = useSession();
  const queryClient = useQueryClient();

  // Import the stagedProductKeys from the staged products query hook
  // This is to ensure we're using the same query keys for invalidation
  const stagedProductKeys = {
    all: ['stagedProducts'] as const,
    lists: () => [...stagedProductKeys.all, 'list'] as const,
  };

  return useMutation({
    mutationFn: ({
      templateId,
      productData
    }: {
      templateId: number;
      productData: {
        title: string;
        images?: Array<{
          imageUrl?: string;
          r2Key?: string;
        }>;
      }
    }) => createStagedProductFromTemplate({
      templateId,
      title: productData.title,
      images: productData.images
    }, session?.backendToken),
    onSuccess: () => {
      // Invalidate staged products list to show the newly created product
      queryClient.invalidateQueries({ queryKey: stagedProductKeys.lists() });
      toast.success('Product created successfully from template');
    },
    onError: (error: Error) => {
      toast.error(`Failed to create product from template: ${error.message}`);
    },
  });
};

/**
 * Hook for creating a staged product from template and immediately uploading to TikTok Shop
 */
export const useCreateStagedProductFromTemplateAndUpload = () => {
  const { data: session } = useSession();
  const queryClient = useQueryClient();

  // Import the stagedProductKeys from the staged products query hook
  // This is to ensure we're using the same query keys for invalidation
  const stagedProductKeys = {
    all: ['stagedProducts'] as const,
    lists: () => [...stagedProductKeys.all, 'list'] as const,
  };

  return useMutation({
    mutationFn: ({
      templateId,
      productData,
      tiktokShopId,
      uploadOptions
    }: {
      templateId: number;
      productData: {
        title: string;
        images?: Array<{
          imageUrl?: string;
          r2Key?: string;
        }>;
      };
      tiktokShopId: number;
      uploadOptions?: {
        skipValidation?: boolean;
        forceUpload?: boolean;
      };
    }) => createStagedProductFromTemplateAndUpload({
      templateId,
      title: productData.title,
      images: productData.images,
      tiktokShopId,
      skipValidation: uploadOptions?.skipValidation,
      forceUpload: uploadOptions?.forceUpload
    }, session?.backendToken),
    onSuccess: (uploadResponse, variables) => {
      // Invalidate staged products list to show the newly created product
      queryClient.invalidateQueries({ queryKey: stagedProductKeys.lists() });

      // Register the upload job with the global job monitor
      if (uploadResponse.jobId) {
        globalJobMonitor.addJob({
          id: uploadResponse.jobId,
          uploadId: uploadResponse.id,
          type: 'product-upload',
          productName: variables.productData.title,
          shopName: uploadResponse.shopFriendlyName,
          startTime: new Date(),
          userId: session?.user?.id?.toString(),
        });
      }

      toast.success('Product created and queued for upload successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to create and upload product: ${error.message}`);
    },
  });
};
